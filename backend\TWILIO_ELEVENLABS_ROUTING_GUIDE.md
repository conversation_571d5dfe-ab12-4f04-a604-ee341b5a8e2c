# Twilio-ElevenLabs Call Routing Implementation Guide

## Phase 5C: Core ElevenLabs Integration - Subtask 2 COMPLETED ✅

This document provides comprehensive information about the Twilio-ElevenLabs call routing system implemented in CallSaver.app Phase 5C.

## 📋 Overview

The Twilio-ElevenLabs call routing system enables automatic routing of incoming Twilio calls to appropriate ElevenLabs AI agents based on phone number assignments. This creates a seamless integration between Twilio's telephony infrastructure and ElevenLabs' conversational AI platform.

## 🏗️ Architecture

### Call Flow
```mermaid
graph TD
    A[Incoming Call to Twilio Number] --> B[Twilio Webhook]
    B --> C[CallSaver Backend]
    C --> D{Find Agent for Phone Number}
    D -->|Agent Found| E[Log Call in Database]
    D -->|No Agent| F[Return Error TwiML]
    E --> G[Return Connection TwiML]
    G --> H[ElevenLabs Handles Conversation]
    F --> I[Caller Hears Error Message]
```

### Database Integration
- **Phone Number to Agent Mapping**: Uses `phone_number_agents` table
- **Call Logging**: Stores call records in `calls` table
- **User Ownership**: RLS policies ensure proper data isolation

## 🔧 Implementation Details

### 1. Enhanced ElevenLabs Service

#### New Methods Added:
- `importPhoneNumber(phoneNumber, twilioCredentials, agentId)` - Import Twilio numbers to ElevenLabs
- `startConversation(agentId, phoneNumber, options)` - Initiate conversations programmatically
- `getAgentPhoneNumbers(agentId)` - Retrieve phone numbers assigned to an agent
- `removePhoneNumber(phoneNumberId)` - Remove phone number from ElevenLabs

#### Enhanced Webhook Processing:
- `handleConversationStarted(eventData)` - Tracks conversations in database
- `handleConversationEnded(eventData)` - Updates call records with completion data

### 2. Twilio Webhook Handler

#### Route: `POST /api/webhooks/twilio/voice`

**Functionality:**
- Receives Twilio voice webhook events
- Extracts call information (CallSid, From, To, CallStatus)
- Finds assigned AI agent for the called number
- Logs incoming calls in database
- Returns appropriate TwiML response

**Response Types:**
- **Agent Found**: Connection message with TwiML
- **No Agent**: Error message directing to support
- **Technical Error**: Graceful error handling

### 3. Agent Lookup System

#### Function: `findAgentForPhoneNumber(phoneNumber)`

**Process:**
1. Query `phone_number_agents` table with joins
2. Filter by phone number and active status
3. Return agent details and phone number ID
4. Handle cases where no agent is assigned

**Database Query:**
```sql
SELECT 
  pna.id,
  pna.phone_number_id,
  pn.id, pn.user_id, pn.number,
  aa.id, aa.user_id, aa.name, aa.elevenlabs_agent_id, aa.is_active
FROM phone_number_agents pna
INNER JOIN phone_numbers pn ON pna.phone_number_id = pn.id
INNER JOIN ai_agents aa ON pna.ai_agent_id = aa.id
WHERE pn.number = $1 
  AND pna.is_active = true 
  AND aa.is_active = true
```

### 4. Call Logging System

#### Function: `logIncomingCall(callData)`

**Stored Data:**
- User ID and phone number ID
- Twilio Call SID (external_id)
- From/To phone numbers
- Call direction (inbound)
- Initial status (in-progress)
- AI analysis metadata

## 🧪 Testing

### Test Coverage

#### 1. Unit Tests (`test-twilio-elevenlabs-routing.js`)
- ✅ Agent lookup functionality
- ✅ Call logging system
- ✅ Database integration
- ✅ User ownership validation

#### 2. Integration Tests (`test-webhook-integration.js`)
- ✅ Complete webhook flow
- ✅ TwiML response generation
- ✅ Database call logging
- ✅ No agent scenario handling
- ✅ Error handling

### Running Tests
```bash
# Unit tests
node test-twilio-elevenlabs-routing.js

# Integration tests  
node test-webhook-integration.js
```

## 📞 Usage Examples

### 1. Setting Up Phone Number Routing

```javascript
// 1. Create AI agent
const agentResult = await fetch('/api/agents', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    name: 'Customer Support Agent',
    description: 'AI assistant for customer support',
    prompt: 'You are a helpful customer support representative...',
    voice_id: 'your-voice-id'
  })
});

// 2. Assign agent to phone number
const assignmentResult = await fetch(`/api/phone-numbers/${phoneNumberId}/agents`, {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    ai_agent_id: agentId
  })
});
```

### 2. Webhook Configuration

**Twilio Console Setup:**
1. Navigate to Phone Numbers → Manage → Active numbers
2. Select your phone number
3. Set Voice webhook URL: `https://your-domain.com/api/webhooks/twilio/voice`
4. Set HTTP method: POST
5. Save configuration

### 3. ElevenLabs Phone Number Import

```javascript
// Import Twilio number to ElevenLabs
const importResult = await elevenLabsService.importPhoneNumber(
  '+**********',
  {
    accountSid: 'your-twilio-account-sid',
    authToken: 'your-twilio-auth-token'
  },
  'elevenlabs-agent-id'
);
```

## 🔒 Security Features

### 1. User Ownership Validation
- RLS policies ensure users only access their data
- Multi-layer validation in application logic
- Proper error handling for unauthorized access

### 2. Webhook Security
- Input validation for all webhook parameters
- Graceful error handling with user-friendly messages
- Logging for security monitoring

### 3. Database Security
- Parameterized queries prevent SQL injection
- Foreign key constraints ensure data integrity
- Soft deletion patterns preserve audit trails

## 🚨 Error Handling

### Common Scenarios

#### 1. No Agent Assigned
**Response:** TwiML with error message
**Action:** Caller hears "no AI assistant configured" message

#### 2. Database Connection Issues
**Response:** Generic error TwiML
**Action:** Graceful degradation with retry logic

#### 3. Invalid Webhook Data
**Response:** 400 Bad Request with TwiML fallback
**Action:** Log error and provide fallback response

## 📊 Monitoring & Analytics

### Call Tracking
- All calls logged in `calls` table
- Conversation start/end events tracked
- User attribution for analytics
- Performance metrics collection

### Database Queries
- Optimized joins for agent lookup
- Indexed columns for performance
- Connection pooling for scalability

## 🔄 Next Steps

### Phase 5C Remaining Tasks:
1. ✅ **Create Phone Number to Agent Mapping System** - COMPLETED
2. ✅ **Implement Twilio-ElevenLabs Call Routing** - COMPLETED
3. 🔄 **Enhance ElevenLabs Webhook Processing** - IN PROGRESS
4. ⏳ **Build Frontend Agent Management Interface**
5. ⏳ **Implement Real-time Call Status Updates**
6. ⏳ **Create End-to-End Integration Tests**

### Immediate Next Actions:
1. Enhance ElevenLabs webhook processing for conversation events
2. Add real-time status updates for active calls
3. Implement conversation transcript storage
4. Add call analytics and reporting features

## 📚 References

- [ElevenLabs Conversational AI API](https://elevenlabs.io/docs/conversational-ai/overview)
- [Twilio Voice Webhooks](https://www.twilio.com/docs/voice/webhooks)
- [CallSaver Phase 5C Implementation Plan](./PHASE5C_IMPLEMENTATION_PLAN.md)
- [Phone Number Agent Mapping Guide](./PHONE_NUMBER_AGENT_MAPPING_GUIDE.md)

---

**Implementation Status**: ✅ **COMPLETED**  
**Next Phase**: Phase 5C Subtask 3 - Enhanced ElevenLabs Webhook Processing  
**Last Updated**: 2025-07-07
