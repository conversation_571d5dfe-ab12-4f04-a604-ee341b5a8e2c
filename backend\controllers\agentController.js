/**
 * Agent Controller for CallSaver Backend
 * Phase 5A: ElevenLabs Conversational AI Integration
 *
 * Handles ElevenLabs conversational AI agent management
 */

const elevenLabsService = require('../services/elevenLabsService');
const { supabase, executeWithErrorHandling } = require('../config/supabase');

/**
 * @desc    Get all agents for authenticated user
 * @route   GET /api/agents
 * @access  Private
 */
const getAgents = async (req, res) => {
  try {
    const userId = req.user.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'User not authenticated properly.',
        code: 'AUTH_REQUIRED'
      });
    }

    // Get user's agents from local database with RLS automatically filtering by user_id
    const dbResult = await executeWithErrorHandling(async () => {
      const { data, error } = await supabase
        .from('ai_agents')
        .select('*')
        .eq('user_id', userId)
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    });

    if (!dbResult.success) {
      console.error('Database error fetching agents:', dbResult.error);
      return res.status(500).json({
        success: false,
        message: 'Failed to fetch agents from database.',
        error: dbResult.error
      });
    }

    // Sync with ElevenLabs API to get latest status for each agent
    const agentsWithStatus = await Promise.all(
      dbResult.data.map(async (dbAgent) => {
        if (dbAgent.elevenlabs_agent_id) {
          const elevenLabsResult = await elevenLabsService.getAgentById(dbAgent.elevenlabs_agent_id);
          return {
            ...dbAgent,
            elevenlabs_status: elevenLabsResult.success ? 'active' : 'inactive',
            elevenlabs_data: elevenLabsResult.success ? elevenLabsResult.agent : null
          };
        }
        return {
          ...dbAgent,
          elevenlabs_status: 'not_synced',
          elevenlabs_data: null
        };
      })
    );

    res.status(200).json({
      success: true,
      data: agentsWithStatus,
      count: agentsWithStatus.length,
      message: 'Agents retrieved successfully'
    });

  } catch (error) {
    console.error('Error fetching agents:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch agents.',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Create a new conversational AI agent
 * @route   POST /api/agents
 * @access  Private
 */
const createAgent = async (req, res) => {
  try {
    const userId = req.user.id;
    const agentData = req.body;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'User not authenticated properly.',
        code: 'AUTH_REQUIRED'
      });
    }

    // Validate required fields
    if (!agentData.name) {
      return res.status(400).json({
        success: false,
        message: 'Agent name is required.',
        code: 'VALIDATION_ERROR'
      });
    }

    // Create agent with database persistence using enhanced service method
    const result = await elevenLabsService.createAgentWithDatabase(agentData, userId);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        message: 'Failed to create agent.',
        error: result.error
      });
    }

    res.status(201).json({
      success: true,
      message: result.message,
      data: result.agent
    });

  } catch (error) {
    console.error('Error creating agent:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create agent.',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Get agent by ID for authenticated user
 * @route   GET /api/agents/:id
 * @access  Private
 */
const getAgentById = async (req, res) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'User not authenticated properly.',
        code: 'AUTH_REQUIRED'
      });
    }

    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'Agent ID is required.',
        code: 'VALIDATION_ERROR'
      });
    }

    // First, verify agent belongs to user via local database
    const dbResult = await executeWithErrorHandling(async () => {
      const { data, error } = await supabase
        .from('ai_agents')
        .select('*')
        .eq('id', id)
        .eq('user_id', userId)
        .eq('is_active', true)
        .single();

      if (error) throw error;
      return data;
    });

    if (!dbResult.success || !dbResult.data) {
      return res.status(404).json({
        success: false,
        message: 'Agent not found or access denied.',
        code: 'NOT_FOUND'
      });
    }

    // Get latest data from ElevenLabs if agent is synced
    let elevenLabsData = null;
    let elevenLabsStatus = 'not_synced';

    if (dbResult.data.elevenlabs_agent_id) {
      const elevenLabsResult = await elevenLabsService.getAgentById(dbResult.data.elevenlabs_agent_id);
      if (elevenLabsResult.success) {
        elevenLabsData = elevenLabsResult.agent;
        elevenLabsStatus = 'active';
      } else {
        elevenLabsStatus = 'inactive';
      }
    }

    res.status(200).json({
      success: true,
      data: {
        ...dbResult.data,
        elevenlabs_status: elevenLabsStatus,
        elevenlabs_data: elevenLabsData
      },
      message: 'Agent retrieved successfully'
    });

  } catch (error) {
    console.error('Error fetching agent by ID:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch agent.',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Update agent by ID for authenticated user
 * @route   PUT /api/agents/:id
 * @access  Private
 */
const updateAgent = async (req, res) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;
    const updateData = req.body;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'User not authenticated properly.',
        code: 'AUTH_REQUIRED'
      });
    }

    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'Agent ID is required.',
        code: 'VALIDATION_ERROR'
      });
    }

    if (!updateData || Object.keys(updateData).length === 0) {
      return res.status(400).json({
        success: false,
        message: 'No valid fields provided for update.',
        code: 'VALIDATION_ERROR'
      });
    }

    // Update agent with database persistence using enhanced service method
    const result = await elevenLabsService.updateAgentWithDatabase(id, updateData, userId);

    if (!result.success) {
      if (result.error.includes('not found') || result.error.includes('access denied')) {
        return res.status(404).json({
          success: false,
          message: 'Agent not found or access denied.',
          code: 'NOT_FOUND'
        });
      }

      return res.status(500).json({
        success: false,
        message: 'Failed to update agent.',
        error: result.error
      });
    }

    res.status(200).json({
      success: true,
      message: result.message,
      data: result.agent
    });

  } catch (error) {
    console.error('Error updating agent:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update agent.',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Delete agent by ID for authenticated user
 * @route   DELETE /api/agents/:id
 * @access  Private
 */
const deleteAgent = async (req, res) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'User not authenticated properly.',
        code: 'AUTH_REQUIRED'
      });
    }

    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'Agent ID is required.',
        code: 'VALIDATION_ERROR'
      });
    }

    // Delete agent with database cleanup using enhanced service method
    const result = await elevenLabsService.deleteAgentWithDatabase(id, userId);

    if (!result.success) {
      if (result.error.includes('not found') || result.error.includes('access denied')) {
        return res.status(404).json({
          success: false,
          message: 'Agent not found or access denied.',
          code: 'NOT_FOUND'
        });
      }

      return res.status(500).json({
        success: false,
        message: 'Failed to delete agent.',
        error: result.error
      });
    }

    res.status(200).json({
      success: true,
      message: result.message,
      data: { id }
    });

  } catch (error) {
    console.error('Error deleting agent:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete agent.',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Test ElevenLabs API connection
 * @route   GET /api/agents/test-connection
 * @access  Private
 */
const testConnection = async (req, res) => {
  try {
    const result = await elevenLabsService.testConnection();
    
    if (result.success) {
      res.status(200).json(result);
    } else {
      res.status(500).json(result);
    }
  } catch (error) {
    console.error('Error testing ElevenLabs connection:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to test connection.',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

module.exports = {
  getAgents,
  createAgent,
  getAgentById,
  updateAgent,
  deleteAgent,
  testConnection,
};
