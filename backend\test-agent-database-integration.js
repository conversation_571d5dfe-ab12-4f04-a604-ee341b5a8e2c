/**
 * Test Agent Database Integration
 * Phase 5B: Authentication & Database Integration
 * 
 * Tests the complete agent CRUD operations with authentication and database persistence
 */

const { supabase, supabaseService, executeWithErrorHandling } = require('./config/supabase');
const elevenLabsService = require('./services/elevenLabsService');

// Test data
const testData = {
  userId1: '550e8400-e29b-41d4-a716-446655440001',
  userId2: '550e8400-e29b-41d4-a716-446655440002',
  testAgentId1: null,
  testAgentId2: null,
  testAgentData: {
    name: 'Test AI Agent',
    description: 'Integration test agent for Phase 5B',
    prompt: 'You are a helpful AI assistant for testing database integration.',
    voice_id: 'test-voice-id',
    voice_settings: { speed: 1.0, stability: 0.5 },
    llm_config: { provider: 'openai', model: 'gpt-4', temperature: 0.7 },
    conversation_config: { max_turns: 10, timeout: 30 },
    knowledge_base: { documents: [], training_status: 'ready' },
    tools: []
  }
};

/**
 * Setup test users in database
 */
async function setupTestUsers() {
  console.log('\n🔧 Setting up test users...');
  
  try {
    // Create test users with unique emails
    const timestamp = Date.now();
    const users = [
      {
        id: testData.userId1,
        email: `test1-${timestamp}@callsaver.app`,
        name: 'Test User 1',
        timezone: 'UTC',
        preferences: {}
      },
      {
        id: testData.userId2,
        email: `test2-${timestamp}@callsaver.app`,
        name: 'Test User 2',
        timezone: 'UTC',
        preferences: {}
      }
    ];

    // First, delete any existing test users
    await supabaseService
      .from('users')
      .delete()
      .in('id', [testData.userId1, testData.userId2]);

    // Then create fresh test users
    for (const user of users) {
      const { data, error } = await supabaseService
        .from('users')
        .insert(user)
        .select();

      if (error) {
        console.error('Error creating user:', user.id, error);
        throw error;
      }
      console.log('User created:', user.id, data ? 'success' : 'no data returned');
    }
    
    console.log('✅ Test users created successfully');
    return true;
  } catch (error) {
    console.error('❌ Error setting up test users:', error.message);
    return false;
  }
}

/**
 * Test agent creation with database persistence
 */
async function testAgentCreation() {
  console.log('\n🧪 Testing agent creation with database persistence...');
  
  try {
    // Test creating agent for user 1
    const result1 = await elevenLabsService.createAgentWithDatabase(
      { ...testData.testAgentData, name: 'Test Agent User 1' },
      testData.userId1
    );
    
    if (!result1.success) {
      console.log('⚠️  ElevenLabs API not available, testing database-only creation...');
      
      // Fallback to database-only creation for testing
      const dbResult = await executeWithErrorHandling(async () => {
        return await supabaseService
          .from('ai_agents')
          .insert({
            user_id: testData.userId1,
            name: 'Test Agent User 1',
            description: testData.testAgentData.description,
            prompt: testData.testAgentData.prompt,
            voice_id: testData.testAgentData.voice_id,
            voice_settings: testData.testAgentData.voice_settings,
            llm_config: testData.testAgentData.llm_config,
            conversation_config: testData.testAgentData.conversation_config,
            knowledge_base: testData.testAgentData.knowledge_base,
            tools: testData.testAgentData.tools,
            is_active: true
          })
          .select()
          .single();
      });
      
      if (!dbResult.success) {
        throw new Error(`Database creation failed: ${dbResult.error}`);
      }


      testData.testAgentId1 = dbResult.data.id;
      console.log('✅ Agent created in database (ElevenLabs API unavailable)');
    } else {
      testData.testAgentId1 = result1.agent.id;
      console.log('✅ Agent created with ElevenLabs integration');
    }
    
    // Test creating agent for user 2
    const dbResult2 = await executeWithErrorHandling(async () => {
      return await supabaseService
        .from('ai_agents')
        .insert({
          user_id: testData.userId2,
          name: 'Test Agent User 2',
          description: testData.testAgentData.description,
          prompt: testData.testAgentData.prompt,
          voice_id: testData.testAgentData.voice_id,
          voice_settings: testData.testAgentData.voice_settings,
          llm_config: testData.testAgentData.llm_config,
          conversation_config: testData.testAgentData.conversation_config,
          knowledge_base: testData.testAgentData.knowledge_base,
          tools: testData.testAgentData.tools,
          is_active: true
        })
        .select()
        .single();
    });
    
    if (!dbResult2.success) {
      throw new Error(`Database creation failed: ${dbResult2.error}`);
    }
    
    testData.testAgentId2 = dbResult2.data.id;
    console.log('✅ Second agent created successfully');
    
    return true;
  } catch (error) {
    console.error('❌ Error testing agent creation:', error.message);
    return false;
  }
}

/**
 * Test user ownership validation
 */
async function testUserOwnership() {
  console.log('\n🔒 Testing user ownership validation...');
  
  try {
    // Test that user 1 can only see their own agents
    const user1Agents = await executeWithErrorHandling(async () => {
      return await supabaseService
        .from('ai_agents')
        .select('*')
        .eq('user_id', testData.userId1)
        .eq('is_active', true);
    });
    
    if (!user1Agents.success) {
      throw new Error(`Failed to fetch user 1 agents: ${user1Agents.error}`);
    }
    
    const user1AgentIds = (user1Agents.data || []).map(agent => agent.id);
    if (!user1AgentIds.includes(testData.testAgentId1)) {
      throw new Error('User 1 cannot see their own agent');
    }
    
    if (user1AgentIds.includes(testData.testAgentId2)) {
      throw new Error('User 1 can see user 2\'s agent (ownership violation)');
    }
    
    console.log('✅ User ownership validation passed');
    
    // Test RLS policies by attempting cross-user access
    const crossUserAccess = await executeWithErrorHandling(async () => {
      return await supabaseService
        .from('ai_agents')
        .select('*')
        .eq('id', testData.testAgentId2)
        .eq('user_id', testData.userId1)
        .single();
    });
    
    if (crossUserAccess.success && crossUserAccess.data) {
      throw new Error('RLS policy failed - user can access other user\'s agent');
    }
    
    console.log('✅ RLS policies working correctly');
    return true;
  } catch (error) {
    console.error('❌ Error testing user ownership:', error.message);
    return false;
  }
}

/**
 * Test agent updates with ownership validation
 */
async function testAgentUpdates() {
  console.log('\n📝 Testing agent updates with ownership validation...');

  try {
    // Test updating user 1's agent directly in database
    const updateResult = await executeWithErrorHandling(async () => {
      return await supabaseService
        .from('ai_agents')
        .update({
          name: 'Updated Test Agent User 1',
          description: 'Updated description for testing',
          updated_at: new Date().toISOString()
        })
        .eq('id', testData.testAgentId1)
        .eq('user_id', testData.userId1)
        .select()
        .single();
    });

    if (!updateResult.success) {
      throw new Error(`Update failed: ${updateResult.error}`);
    }

    console.log('✅ Agent update successful');

    // Test that user 2 cannot update user 1's agent (should return no rows)
    const unauthorizedUpdate = await executeWithErrorHandling(async () => {
      return await supabaseService
        .from('ai_agents')
        .update({ name: 'Unauthorized Update' })
        .eq('id', testData.testAgentId1)
        .eq('user_id', testData.userId2)
        .select();
    });

    if (unauthorizedUpdate.success && unauthorizedUpdate.data && unauthorizedUpdate.data.length > 0) {
      throw new Error('Unauthorized update succeeded (ownership validation failed)');
    }

    console.log('✅ Unauthorized update properly blocked');
    return true;
  } catch (error) {
    console.error('❌ Error testing agent updates:', error.message);
    return false;
  }
}

/**
 * Test agent deletion with ownership validation
 */
async function testAgentDeletion() {
  console.log('\n🗑️  Testing agent deletion with ownership validation...');

  try {
    // Test that user 2 cannot delete user 1's agent (should return no rows)
    const unauthorizedDelete = await executeWithErrorHandling(async () => {
      return await supabaseService
        .from('ai_agents')
        .update({
          is_active: false,
          updated_at: new Date().toISOString()
        })
        .eq('id', testData.testAgentId1)
        .eq('user_id', testData.userId2)
        .select();
    });

    if (unauthorizedDelete.success && unauthorizedDelete.data && unauthorizedDelete.data.length > 0) {
      throw new Error('Unauthorized deletion succeeded (ownership validation failed)');
    }

    console.log('✅ Unauthorized deletion properly blocked');

    // Test that user 1 can delete their own agent
    const authorizedDelete = await executeWithErrorHandling(async () => {
      return await supabaseService
        .from('ai_agents')
        .update({
          is_active: false,
          updated_at: new Date().toISOString()
        })
        .eq('id', testData.testAgentId1)
        .eq('user_id', testData.userId1)
        .select()
        .single();
    });

    if (!authorizedDelete.success) {
      throw new Error(`Authorized deletion failed: ${authorizedDelete.error}`);
    }

    console.log('✅ Authorized deletion successful');

    // Verify agent is soft-deleted (is_active = false)
    const deletedAgent = await executeWithErrorHandling(async () => {
      return await supabaseService
        .from('ai_agents')
        .select('*')
        .eq('id', testData.testAgentId1)
        .single();
    });

    if (!deletedAgent.success || deletedAgent.data.is_active) {
      throw new Error('Agent not properly soft-deleted');
    }

    console.log('✅ Agent properly soft-deleted');
    return true;
  } catch (error) {
    console.error('❌ Error testing agent deletion:', error.message);
    return false;
  }
}

/**
 * Cleanup test data
 */
async function cleanup() {
  console.log('\n🧹 Cleaning up test data...');
  
  try {
    // Delete test agents
    await supabaseService
      .from('ai_agents')
      .delete()
      .in('user_id', [testData.userId1, testData.userId2]);

    // Delete test users
    await supabaseService
      .from('users')
      .delete()
      .in('id', [testData.userId1, testData.userId2]);

    console.log('✅ Cleanup completed');
    return true;
  } catch (error) {
    console.error('❌ Error during cleanup:', error.message);
    return false;
  }
}

/**
 * Run all tests
 */
async function runTests() {
  console.log('🚀 Starting Agent Database Integration Tests');
  console.log('='.repeat(50));
  
  const results = {
    setup: false,
    creation: false,
    ownership: false,
    updates: false,
    deletion: false,
    cleanup: false
  };
  
  try {
    results.setup = await setupTestUsers();
    if (!results.setup) throw new Error('Setup failed');
    
    results.creation = await testAgentCreation();
    if (!results.creation) throw new Error('Creation tests failed');
    
    results.ownership = await testUserOwnership();
    if (!results.ownership) throw new Error('Ownership tests failed');
    
    results.updates = await testAgentUpdates();
    if (!results.updates) throw new Error('Update tests failed');
    
    results.deletion = await testAgentDeletion();
    if (!results.deletion) throw new Error('Deletion tests failed');
    
  } catch (error) {
    console.error(`\n💥 Test suite failed: ${error.message}`);
  } finally {
    results.cleanup = await cleanup();
  }
  
  // Print results summary
  console.log('\n📊 Test Results Summary');
  console.log('='.repeat(30));
  Object.entries(results).forEach(([test, passed]) => {
    console.log(`${passed ? '✅' : '❌'} ${test.charAt(0).toUpperCase() + test.slice(1)}: ${passed ? 'PASSED' : 'FAILED'}`);
  });
  
  const allPassed = Object.values(results).every(result => result);
  console.log(`\n🎯 Overall Result: ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);
  
  return allPassed;
}

// Run tests if called directly
if (require.main === module) {
  runTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Fatal error:', error);
      process.exit(1);
    });
}

module.exports = { runTests };
