const axios = require('axios');
const crypto = require('crypto');
require('dotenv').config();

/**
 * ElevenLabs Conversational AI Service
 * Handles all interactions with ElevenLabs API for agent management and operations
 */
class ElevenLabsService {
  constructor() {
    this.apiKey = process.env.ELEVENLABS_API_KEY;
    this.baseUrl = process.env.ELEVENLABS_BASE_URL || 'https://api.elevenlabs.io/v1';
    this.webhookSecret = process.env.ELEVENLABS_WEBHOOK_SECRET;
    
    if (!this.apiKey) {
      throw new Error('ELEVENLABS_API_KEY is required');
    }
    
    // Configure axios instance with default headers
    this.client = axios.create({
      baseURL: this.baseUrl,
      headers: {
        'xi-api-key': this.apiKey,
        'Content-Type': 'application/json',
      },
      timeout: 30000, // 30 second timeout
    });
    
    // Add request/response interceptors for logging and error handling
    this.setupInterceptors();
  }
  
  /**
   * Setup axios interceptors for logging and error handling
   */
  setupInterceptors() {
    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        console.log(`[ElevenLabs] ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('[ElevenLabs] Request error:', error.message);
        return Promise.reject(error);
      }
    );
    
    // Response interceptor
    this.client.interceptors.response.use(
      (response) => {
        console.log(`[ElevenLabs] Response ${response.status} from ${response.config.url}`);
        return response;
      },
      (error) => {
        const message = error.response?.data?.detail || error.message;
        console.error(`[ElevenLabs] API Error: ${message}`);
        return Promise.reject(new Error(`ElevenLabs API Error: ${message}`));
      }
    );
  }
  
  /**
   * Test API connectivity and authentication
   */
  async testConnection() {
    try {
      const response = await this.client.get('/user');
      console.log('[ElevenLabs] Connection test successful');
      return {
        success: true,
        user: response.data,
        message: 'ElevenLabs API connection successful'
      };
    } catch (error) {
      console.error('[ElevenLabs] Connection test failed:', error.message);
      return {
        success: false,
        error: error.message,
        message: 'ElevenLabs API connection failed'
      };
    }
  }
  
  /**
   * Get all conversational AI agents
   */
  async getAllAgents() {
    try {
      const response = await this.client.get('/convai/agents');
      return {
        success: true,
        agents: response.data.agents || [],
        message: 'Agents retrieved successfully'
      };
    } catch (error) {
      console.error('[ElevenLabs] Error fetching agents:', error.message);
      return {
        success: false,
        error: error.message,
        agents: []
      };
    }
  }
  
  /**
   * Get a specific agent by ID
   */
  async getAgentById(agentId) {
    try {
      if (!agentId) {
        throw new Error('Agent ID is required');
      }
      
      const response = await this.client.get(`/convai/agents/${agentId}`);
      return {
        success: true,
        agent: response.data,
        message: 'Agent retrieved successfully'
      };
    } catch (error) {
      console.error(`[ElevenLabs] Error fetching agent ${agentId}:`, error.message);
      return {
        success: false,
        error: error.message,
        agent: null
      };
    }
  }
  
  /**
   * Create a new conversational AI agent
   */
  async createAgent(agentData) {
    try {
      if (!agentData || !agentData.name) {
        throw new Error('Agent name is required');
      }
      
      const response = await this.client.post('/convai/agents', agentData);
      console.log(`[ElevenLabs] Agent created: ${response.data.agent_id}`);
      
      return {
        success: true,
        agent: response.data,
        message: 'Agent created successfully'
      };
    } catch (error) {
      console.error('[ElevenLabs] Error creating agent:', error.message);
      return {
        success: false,
        error: error.message,
        agent: null
      };
    }
  }
  
  /**
   * Update an existing agent
   */
  async updateAgent(agentId, updateData) {
    try {
      if (!agentId) {
        throw new Error('Agent ID is required');
      }
      
      const response = await this.client.patch(`/convai/agents/${agentId}`, updateData);
      console.log(`[ElevenLabs] Agent updated: ${agentId}`);
      
      return {
        success: true,
        agent: response.data,
        message: 'Agent updated successfully'
      };
    } catch (error) {
      console.error(`[ElevenLabs] Error updating agent ${agentId}:`, error.message);
      return {
        success: false,
        error: error.message,
        agent: null
      };
    }
  }
  
  /**
   * Delete an agent
   */
  async deleteAgent(agentId) {
    try {
      if (!agentId) {
        throw new Error('Agent ID is required');
      }
      
      await this.client.delete(`/convai/agents/${agentId}`);
      console.log(`[ElevenLabs] Agent deleted: ${agentId}`);
      
      return {
        success: true,
        message: 'Agent deleted successfully'
      };
    } catch (error) {
      console.error(`[ElevenLabs] Error deleting agent ${agentId}:`, error.message);
      return {
        success: false,
        error: error.message
      };
    }
  }
  
  /**
   * Validate webhook signature for security
   */
  validateWebhookSignature(payload, signature) {
    if (!this.webhookSecret) {
      console.warn('[ElevenLabs] Webhook secret not configured');
      return false;
    }
    
    try {
      const expectedSignature = crypto
        .createHmac('sha256', this.webhookSecret)
        .update(payload)
        .digest('hex');
      
      return crypto.timingSafeEqual(
        Buffer.from(signature, 'hex'),
        Buffer.from(expectedSignature, 'hex')
      );
    } catch (error) {
      console.error('[ElevenLabs] Webhook signature validation error:', error.message);
      return false;
    }
  }
  
  /**
   * Process webhook events from ElevenLabs
   */
  async processWebhookEvent(eventType, eventData) {
    try {
      console.log(`[ElevenLabs] Processing webhook event: ${eventType}`);
      
      switch (eventType) {
        case 'conversation.started':
          return await this.handleConversationStarted(eventData);
        case 'conversation.ended':
          return await this.handleConversationEnded(eventData);
        case 'agent.updated':
          return await this.handleAgentUpdated(eventData);
        default:
          console.log(`[ElevenLabs] Unhandled event type: ${eventType}`);
          return { success: true, message: 'Event acknowledged' };
      }
    } catch (error) {
      console.error(`[ElevenLabs] Error processing webhook event ${eventType}:`, error.message);
      return { success: false, error: error.message };
    }
  }
  
  /**
   * Handle conversation started event
   */
  async handleConversationStarted(eventData) {
    console.log('[ElevenLabs] Conversation started:', eventData.conversation_id);
    // TODO: Implement conversation tracking in database
    return { success: true, message: 'Conversation started event processed' };
  }
  
  /**
   * Handle conversation ended event
   */
  async handleConversationEnded(eventData) {
    console.log('[ElevenLabs] Conversation ended:', eventData.conversation_id);
    // TODO: Implement conversation completion tracking
    return { success: true, message: 'Conversation ended event processed' };
  }
  
  /**
   * Handle agent updated event
   */
  async handleAgentUpdated(eventData) {
    console.log('[ElevenLabs] Agent updated:', eventData.agent_id);

    try {
      // Sync agent changes with local database
      const syncResult = await this.syncAgentWithDatabase(eventData.agent_id);
      if (syncResult.success) {
        console.log(`[ElevenLabs] Agent ${eventData.agent_id} synced with database`);
      } else {
        console.error(`[ElevenLabs] Failed to sync agent ${eventData.agent_id}:`, syncResult.error);
      }

      return { success: true, message: 'Agent updated event processed' };
    } catch (error) {
      console.error('[ElevenLabs] Error handling agent updated event:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Sync agent data between ElevenLabs API and local database
   */
  async syncAgentWithDatabase(elevenLabsAgentId) {
    try {
      const { supabase, executeWithErrorHandling } = require('../config/supabase');

      // Get agent data from ElevenLabs
      const elevenLabsResult = await this.getAgentById(elevenLabsAgentId);
      if (!elevenLabsResult.success) {
        return {
          success: false,
          error: `Failed to fetch agent from ElevenLabs: ${elevenLabsResult.error}`
        };
      }

      // Update local database with latest ElevenLabs data
      const dbResult = await executeWithErrorHandling(async () => {
        const agent = elevenLabsResult.agent;

        const { data, error } = await supabase
          .from('ai_agents')
          .update({
            name: agent.name || '',
            description: agent.description || '',
            prompt: agent.system_prompt || agent.prompt || '',
            voice_id: agent.voice?.voice_id || '',
            voice_settings: agent.voice?.settings || {},
            llm_config: agent.llm || {},
            conversation_config: agent.conversation_config || {},
            updated_at: new Date().toISOString()
          })
          .eq('elevenlabs_agent_id', elevenLabsAgentId)
          .select();

        if (error) throw error;
        return data;
      });

      if (!dbResult.success) {
        return {
          success: false,
          error: `Database sync failed: ${dbResult.error}`
        };
      }

      return {
        success: true,
        message: 'Agent synced successfully',
        data: dbResult.data
      };

    } catch (error) {
      console.error('[ElevenLabs] Error syncing agent with database:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Create agent with database persistence
   */
  async createAgentWithDatabase(agentData, userId) {
    try {
      const { supabase, executeWithErrorHandling } = require('../config/supabase');

      // Create agent in ElevenLabs first
      const elevenLabsResult = await this.createAgent(agentData);
      if (!elevenLabsResult.success) {
        return elevenLabsResult;
      }

      // Store in local database
      const dbResult = await executeWithErrorHandling(async () => {
        const { data, error } = await supabase
          .from('ai_agents')
          .insert({
            user_id: userId,
            name: agentData.name,
            description: agentData.description || '',
            elevenlabs_agent_id: elevenLabsResult.agent.agent_id,
            prompt: agentData.prompt || agentData.system_prompt || '',
            voice_id: agentData.voice?.voice_id || agentData.voice_id || '',
            voice_settings: agentData.voice?.settings || agentData.voice_settings || {},
            llm_config: agentData.llm || agentData.llm_config || { provider: 'openai', model: 'gpt-4' },
            conversation_config: agentData.conversation_config || {},
            knowledge_base: agentData.knowledge_base || {},
            tools: agentData.tools || [],
            is_active: true
          })
          .select()
          .single();

        if (error) throw error;
        return data;
      });

      if (!dbResult.success) {
        // Cleanup ElevenLabs agent if database storage failed
        await this.deleteAgent(elevenLabsResult.agent.agent_id);
        return {
          success: false,
          error: `Database storage failed: ${dbResult.error}`
        };
      }

      return {
        success: true,
        agent: {
          ...dbResult.data,
          elevenlabs_data: elevenLabsResult.agent
        },
        message: 'Agent created and stored successfully'
      };

    } catch (error) {
      console.error('[ElevenLabs] Error creating agent with database:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Update agent with database persistence
   */
  async updateAgentWithDatabase(agentId, updateData, userId) {
    try {
      const { supabase, executeWithErrorHandling } = require('../config/supabase');

      // First verify agent belongs to user
      const dbResult = await executeWithErrorHandling(async () => {
        const { data, error } = await supabase
          .from('ai_agents')
          .select('*')
          .eq('id', agentId)
          .eq('user_id', userId)
          .eq('is_active', true)
          .single();

        if (error) throw error;
        return data;
      });

      if (!dbResult.success || !dbResult.data) {
        return {
          success: false,
          error: 'Agent not found or access denied'
        };
      }

      // Update in ElevenLabs if synced
      let elevenLabsResult = null;
      if (dbResult.data.elevenlabs_agent_id) {
        elevenLabsResult = await this.updateAgent(dbResult.data.elevenlabs_agent_id, updateData);
        if (!elevenLabsResult.success) {
          return {
            success: false,
            error: `ElevenLabs update failed: ${elevenLabsResult.error}`
          };
        }
      }

      // Update in local database
      const updateDbResult = await executeWithErrorHandling(async () => {
        const updateFields = {};

        // Map update fields to database schema
        if (updateData.name) updateFields.name = updateData.name;
        if (updateData.description !== undefined) updateFields.description = updateData.description;
        if (updateData.prompt || updateData.system_prompt) updateFields.prompt = updateData.prompt || updateData.system_prompt;
        if (updateData.voice_id || updateData.voice?.voice_id) updateFields.voice_id = updateData.voice_id || updateData.voice?.voice_id;
        if (updateData.voice_settings || updateData.voice?.settings) updateFields.voice_settings = updateData.voice_settings || updateData.voice?.settings;
        if (updateData.llm_config || updateData.llm) updateFields.llm_config = updateData.llm_config || updateData.llm;
        if (updateData.conversation_config) updateFields.conversation_config = updateData.conversation_config;
        if (updateData.knowledge_base) updateFields.knowledge_base = updateData.knowledge_base;
        if (updateData.tools) updateFields.tools = updateData.tools;

        updateFields.updated_at = new Date().toISOString();

        const { data, error } = await supabase
          .from('ai_agents')
          .update(updateFields)
          .eq('id', agentId)
          .eq('user_id', userId)
          .select()
          .single();

        if (error) throw error;
        return data;
      });

      if (!updateDbResult.success) {
        return {
          success: false,
          error: `Database update failed: ${updateDbResult.error}`
        };
      }

      return {
        success: true,
        agent: {
          ...updateDbResult.data,
          elevenlabs_data: elevenLabsResult?.agent || null
        },
        message: 'Agent updated successfully'
      };

    } catch (error) {
      console.error('[ElevenLabs] Error updating agent with database:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Delete agent with database cleanup
   */
  async deleteAgentWithDatabase(agentId, userId) {
    try {
      const { supabase, executeWithErrorHandling } = require('../config/supabase');

      // First verify agent belongs to user
      const dbResult = await executeWithErrorHandling(async () => {
        const { data, error } = await supabase
          .from('ai_agents')
          .select('*')
          .eq('id', agentId)
          .eq('user_id', userId)
          .eq('is_active', true)
          .single();

        if (error) throw error;
        return data;
      });

      if (!dbResult.success || !dbResult.data) {
        return {
          success: false,
          error: 'Agent not found or access denied'
        };
      }

      // Delete from ElevenLabs if synced
      if (dbResult.data.elevenlabs_agent_id) {
        const elevenLabsResult = await this.deleteAgent(dbResult.data.elevenlabs_agent_id);
        if (!elevenLabsResult.success) {
          console.warn(`Failed to delete agent from ElevenLabs: ${elevenLabsResult.error}`);
          // Continue with local deletion even if ElevenLabs deletion fails
        }
      }

      // Soft delete from local database
      const deleteDbResult = await executeWithErrorHandling(async () => {
        const { data, error } = await supabase
          .from('ai_agents')
          .update({
            is_active: false,
            updated_at: new Date().toISOString()
          })
          .eq('id', agentId)
          .eq('user_id', userId)
          .select()
          .single();

        if (error) throw error;
        return data;
      });

      if (!deleteDbResult.success) {
        return {
          success: false,
          error: `Database deletion failed: ${deleteDbResult.error}`
        };
      }

      return {
        success: true,
        message: 'Agent deleted successfully'
      };

    } catch (error) {
      console.error('[ElevenLabs] Error deleting agent with database:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

// Export singleton instance
module.exports = new ElevenLabsService();
